using CoreHub.Shared.Services;

namespace CoreHub.Web.Services
{
    public class WebQrCodeScannerService : IQrCodeScannerService
    {
        public Task<string?> ScanQrCodeAsync()
        {
            // Web端不支持摄像头扫描，返回空字符串
            return Task.FromResult<string?>(string.Empty);
        }

        public Task<bool> RequestCameraPermissionAsync()
        {
            // Web端不需要摄像头权限，返回false
            return Task.FromResult(false);
        }

        public bool IsCameraAvailable()
        {
            // Web端不支持摄像头，返回false
            return false;
        }
    }
} 