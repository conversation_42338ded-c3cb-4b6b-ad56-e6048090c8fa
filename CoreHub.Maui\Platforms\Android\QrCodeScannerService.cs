using CoreHub.Shared.Services;
using ZXing.Net.Maui;
using Microsoft.Maui.Controls;
using ZXing.Net.Maui.Controls;

namespace CoreHub.Platforms.Android
{
    public class QrCodeScannerService : IQrCodeScannerService
    {
        private TaskCompletionSource<string?>? _scanTaskCompletionSource;
        private string _lastDetectedBarcode = string.Empty;
        private DateTime _lastDetectedTime = DateTime.MinValue;
        private bool _isProcessing = false; // 添加处理状态标志

        public async Task<string?> ScanQrCodeAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("开始扫描流程...");
                
                // 重置状态
                _isProcessing = false;
                _lastDetectedBarcode = string.Empty;
                _lastDetectedTime = DateTime.MinValue;
                
                // 检查权限
                if (!await RequestCameraPermissionAsync())
                {
                    System.Diagnostics.Debug.WriteLine("摄像头权限检查失败");
                    return null;
                }

                // 检查摄像头是否可用
                if (!IsCameraAvailable())
                {
                    System.Diagnostics.Debug.WriteLine("摄像头不可用");
                    return null;
                }

                System.Diagnostics.Debug.WriteLine("权限和摄像头检查通过，创建扫描任务...");

                // 创建扫描任务
                _scanTaskCompletionSource = new TaskCompletionSource<string?>();

                // 在主线程上启动扫描页面
                await MainThread.InvokeOnMainThreadAsync(async () =>
                {
                    try
                    {
                        System.Diagnostics.Debug.WriteLine("在主线程创建扫描页面...");
                        
                        // 创建扫描页面
                        var scanPage = new ContentPage
                        {
                            Title = "扫描二维码",
                            BackgroundColor = Colors.Black
                        };

                        // 使用官方推荐的配置方式
                        var cameraView = new CameraBarcodeReaderView
                        {
                            IsDetecting = true,
                            VerticalOptions = LayoutOptions.FillAndExpand,
                            HorizontalOptions = LayoutOptions.FillAndExpand,
                            Options = new BarcodeReaderOptions
                            {
                                Formats = BarcodeFormat.QrCode | BarcodeFormat.Ean13 | BarcodeFormat.Code128, // 指定具体格式
                                AutoRotate = true,
                                Multiple = false // 单次扫描
                            }
                        };

                        System.Diagnostics.Debug.WriteLine("CameraBarcodeReaderView已创建（官方配置），设置事件处理...");

                        // 处理扫描结果 - 使用官方推荐的方式
                        cameraView.BarcodesDetected += (sender, e) =>
                        {
                            // 防止重复处理
                            if (_isProcessing)
                            {
                                System.Diagnostics.Debug.WriteLine("正在处理中，忽略此次检测");
                                return;
                            }

                            System.Diagnostics.Debug.WriteLine($"BarcodesDetected事件触发，结果数量: {e.Results?.Count() ?? 0}");
                            
                            var first = e.Results?.FirstOrDefault();
                            if (first is null)
                            {
                                System.Diagnostics.Debug.WriteLine("BarcodesDetected事件触发但没有有效结果");
                                return;
                            }

                            // 防止重复检测同一个条码
                            if (first.Value == _lastDetectedBarcode && (DateTime.Now - _lastDetectedTime).TotalSeconds < 2)
                            {
                                System.Diagnostics.Debug.WriteLine("忽略重复检测的条码");
                                return;
                            }

                            // 设置处理状态
                            _isProcessing = true;
                            _lastDetectedBarcode = first.Value;
                            _lastDetectedTime = DateTime.Now;

                            System.Diagnostics.Debug.WriteLine($"扫描到条码: {first.Value}, 格式: {first.Format}");
                            
                            // 立即停止检测
                            cameraView.IsDetecting = false;
                            
                            // 使用Dispatcher确保在主线程执行
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                try
                                {
                                    // 检查TaskCompletionSource状态
                                    if (_scanTaskCompletionSource?.Task.IsCompleted == true)
                                    {
                                        System.Diagnostics.Debug.WriteLine("任务已完成，跳过处理");
                                        return;
                                    }

                                    System.Diagnostics.Debug.WriteLine("关闭扫描页面并返回结果...");
                                    
                                    // 检查导航栈状态
                                    var navigation = Application.Current?.MainPage?.Navigation;
                                    if (navigation?.ModalStack?.Count > 0)
                                    {
                                        await navigation.PopModalAsync();
                                        System.Diagnostics.Debug.WriteLine("页面关闭成功");
                                    }
                                    else
                                    {
                                        System.Diagnostics.Debug.WriteLine("模态栈为空，跳过页面关闭");
                                    }
                                    
                                    // 设置结果
                                    _scanTaskCompletionSource?.TrySetResult(first.Value);
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"关闭页面时出错: {ex.Message}");
                                    // 仍然尝试设置结果
                                    _scanTaskCompletionSource?.TrySetResult(first.Value);
                                }
                            });
                        };

                        // 创建取消按钮
                        var cancelButton = new Button
                        {
                            Text = "取消扫描",
                            BackgroundColor = Colors.Red,
                            TextColor = Colors.White,
                            Margin = new Thickness(20),
                            VerticalOptions = LayoutOptions.End
                        };

                        cancelButton.Clicked += async (sender, e) =>
                        {
                            System.Diagnostics.Debug.WriteLine("用户点击取消扫描");
                            
                            if (!_isProcessing)
                            {
                                _isProcessing = true;
                                cameraView.IsDetecting = false;
                                
                                try
                                {
                                    await Application.Current?.MainPage?.Navigation.PopModalAsync();
                                }
                                catch (Exception ex)
                                {
                                    System.Diagnostics.Debug.WriteLine($"取消时关闭页面失败: {ex.Message}");
                                }
                                
                                _scanTaskCompletionSource?.TrySetResult(null);
                            }
                        };

                        // 创建标题标签
                        var titleLabel = new Label
                        {
                            Text = "请将二维码对准摄像头",
                            TextColor = Colors.White,
                            FontSize = 18,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = new Thickness(20, 40, 20, 20)
                        };

                        // 创建状态标签
                        var statusLabel = new Label
                        {
                            Text = "正在启动摄像头...",
                            TextColor = Colors.Yellow,
                            FontSize = 14,
                            HorizontalOptions = LayoutOptions.Center,
                            Margin = new Thickness(20, 10, 20, 10)
                        };

                        // 创建扫描框指示器
                        var scanFrame = new Frame
                        {
                            BackgroundColor = Colors.Transparent,
                            BorderColor = Colors.Lime,
                            HasShadow = false,
                            WidthRequest = 200,
                            HeightRequest = 200,
                            HorizontalOptions = LayoutOptions.Center,
                            VerticalOptions = LayoutOptions.Center,
                            Content = new Label
                            {
                                Text = "对准此区域",
                                TextColor = Colors.Lime,
                                FontSize = 12,
                                HorizontalOptions = LayoutOptions.Center,
                                VerticalOptions = LayoutOptions.Center
                            }
                        };

                        // 使用Grid布局
                        var grid = new Grid
                        {
                            RowDefinitions = 
                            {
                                new RowDefinition { Height = GridLength.Auto },
                                new RowDefinition { Height = GridLength.Auto },
                                new RowDefinition { Height = GridLength.Star },
                                new RowDefinition { Height = GridLength.Auto }
                            },
                            BackgroundColor = Colors.Black
                        };

                        // 添加控件到网格
                        grid.Add(titleLabel, 0, 0);
                        grid.Add(statusLabel, 0, 1);
                        grid.Add(cameraView, 0, 2);
                        grid.Add(scanFrame, 0, 2); // 覆盖在摄像头视图上
                        grid.Add(cancelButton, 0, 3);

                        scanPage.Content = grid;

                        // 页面出现时更新状态
                        scanPage.Appearing += (sender, e) =>
                        {
                            System.Diagnostics.Debug.WriteLine("扫描页面已显示");
                            statusLabel.Text = "摄像头已启动，请对准条码";
                            
                            // 确保检测状态正确
                            Device.StartTimer(TimeSpan.FromSeconds(0.5), () =>
                            {
                                System.Diagnostics.Debug.WriteLine($"检查IsDetecting状态: {cameraView.IsDetecting}");
                                if (!cameraView.IsDetecting && !_isProcessing)
                                {
                                    System.Diagnostics.Debug.WriteLine("重新启用检测...");
                                    cameraView.IsDetecting = true;
                                }
                                return false; // 只执行一次
                            });
                        };

                        System.Diagnostics.Debug.WriteLine("推送扫描页面...");
                        
                        // 使用模态页面推送扫描页面
                        await Application.Current?.MainPage?.Navigation.PushModalAsync(scanPage);
                        
                        System.Diagnostics.Debug.WriteLine("扫描页面已推送");
                    }
                    catch (Exception ex)
                    {
                        System.Diagnostics.Debug.WriteLine($"启动扫描页面失败: {ex.Message}");
                        System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                        _scanTaskCompletionSource?.TrySetResult(null);
                    }
                });

                System.Diagnostics.Debug.WriteLine("等待扫描结果...");
                
                // 等待扫描结果
                var result = await _scanTaskCompletionSource.Task;
                
                System.Diagnostics.Debug.WriteLine($"扫描完成，结果: {result ?? "null"}");
                
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"扫描二维码失败: {ex.Message}");
                System.Diagnostics.Debug.WriteLine($"异常详情: {ex}");
                _scanTaskCompletionSource?.TrySetResult(null);
                return null;
            }
        }

        public async Task<bool> RequestCameraPermissionAsync()
        {
            try
            {
                System.Diagnostics.Debug.WriteLine("请求摄像头权限...");
                var status = await Permissions.RequestAsync<Permissions.Camera>();
                System.Diagnostics.Debug.WriteLine($"摄像头权限状态: {status}");
                return status == PermissionStatus.Granted;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"请求摄像头权限失败: {ex.Message}");
                return false;
            }
        }

        public bool IsCameraAvailable()
        {
            try
            {
                var context = Platform.CurrentActivity ?? global::Android.App.Application.Context;
                var packageManager = context.PackageManager;
                var hasCamera = packageManager?.HasSystemFeature(global::Android.Content.PM.PackageManager.FeatureCamera) == true;
                System.Diagnostics.Debug.WriteLine($"摄像头可用性: {hasCamera}");
                return hasCamera;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"检查摄像头可用性失败: {ex.Message}");
                return false;
            }
        }
    }
} 