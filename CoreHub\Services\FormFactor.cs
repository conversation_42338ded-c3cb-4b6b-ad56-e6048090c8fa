using CoreHub.Shared.Services;

namespace CoreHub.Services
{
    public class FormFactor : IFormFactor
    {
        public string GetFormFactor()
        {
#if ANDROID
            return "Android";
#elif IOS
            return "iOS";
#elif MACCATALYST
            return "MacCatalyst";
#elif WINDOWS
            return "Windows";
#else
            return "Mobile";
#endif
        }

        public string GetPlatform()
        {
            return DeviceInfo.Platform.ToString();
        }
    }
} 