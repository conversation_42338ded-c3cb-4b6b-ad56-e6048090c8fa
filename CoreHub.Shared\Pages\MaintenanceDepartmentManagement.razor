@page "/maintenance-department-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MudBlazor
@inject IMaintenanceDepartmentPermissionService MaintenanceDepartmentPermissionService
@inject IDepartmentService DepartmentService
@inject IDepartmentTypeService DepartmentTypeService
@inject AuthenticationStateProvider AuthStateProvider
@inject ISnackbar Snackbar
@inject IDialogService DialogService

<PageTitle>维修部门管理</PageTitle>

<AuthorizeView>
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudGrid>
                    <MudItem xs="12">
                        <MudText Typo="Typo.h4" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.Engineering" Class="mr-2" />
                            维修部门管理
                        </MudText>
                        <MudText Typo="Typo.body1" Color="Color.Secondary" Class="mb-4">
                            管理维修部门与生产部门之间的维修权限关系，设置哪些维修部门可以维修哪些生产部门的设备。
                        </MudText>
                    </MudItem>

                    <!-- 维修部门权限分配 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">维修权限分配</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="12" md="4">
                                        <MudSelect T="Department" @bind-Value="selectedMaintenanceDepartment" Label="选择维修部门" 
                                            Variant="Variant.Outlined" AnchorOrigin="Origin.BottomCenter">
                                            @foreach (var dept in maintenanceDepartments)
                                            {
                                                <MudSelectItem Value="@dept">
                                                    <div class="d-flex align-center">
                                                        <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                        <span>@dept.Name</span>
                                                        @if (dept.DepartmentType != null)
                                                        {
                                                            <MudChip T="string" Color="Color.Info" Size="Size.Small" Class="ml-2">
                                                                @dept.DepartmentType.Name
                                                            </MudChip>
                                                        }
                                                    </div>
                                                </MudSelectItem>
                                            }
                                        </MudSelect>
                                    </MudItem>
                                    <MudItem xs="12" md="8">
                                        @if (selectedMaintenanceDepartment != null)
                                        {
                                            <MudText Typo="Typo.subtitle1" Class="mb-3">
                                                为 "@selectedMaintenanceDepartment.Name" 分配可维修的部门
                                            </MudText>
                                            <MudGrid>
                                                @foreach (var dept in productionDepartments)
                                                {
                                                    <MudItem xs="6" sm="4" md="3">
                                                        <MudCheckBox T="bool" 
                                                            Checked="@IsMaintenancePermissionAssigned(selectedMaintenanceDepartment.Id, dept.Id)"
                                                            CheckedChanged="@((bool isChecked) => OnMaintenancePermissionChanged(selectedMaintenanceDepartment.Id, dept.Id, isChecked))"
                                                            Label="@dept.Name"
                                                            Color="Color.Primary" />
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                            <MudDivider Class="my-4" />
                                            <MudButton Variant="Variant.Filled" Color="Color.Primary" 
                                                OnClick="SaveMaintenancePermissions" 
                                                StartIcon="@Icons.Material.Filled.Save"
                                                Disabled="@saving">
                                                @if (saving)
                                                {
                                                    <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                                    <MudText Class="ms-2">保存中...</MudText>
                                                }
                                                else
                                                {
                                                    <MudText>保存权限</MudText>
                                                }
                                            </MudButton>
                                        }
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 权限矩阵视图 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">维修权限矩阵</MudText>
                                </CardHeaderContent>
                                <CardHeaderActions>
                                    <MudButton Variant="Variant.Text" Color="Color.Primary" 
                                        StartIcon="@Icons.Material.Filled.Refresh" OnClick="LoadData">
                                        刷新
                                    </MudButton>
                                </CardHeaderActions>
                            </MudCardHeader>
                            <MudCardContent>
                                @if (loading)
                                {
                                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
                                }
                                else
                                {
                                    <MudTable Items="@permissionMatrix" Hover="true" Dense="true" Striped="true">
                                        <HeaderContent>
                                            <MudTh>维修部门</MudTh>
                                            @foreach (var dept in productionDepartments)
                                            {
                                                <MudTh Style="text-align: center; min-width: 100px;">
                                                    <MudText Typo="Typo.caption">@dept.Name</MudText>
                                                </MudTh>
                                            }
                                            <MudTh Style="text-align: center;">可维修部门数</MudTh>
                                        </HeaderContent>
                                        <RowTemplate Context="row">
                                            <MudTd DataLabel="维修部门">
                                                <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                    @row.MaintenanceDepartmentName
                                                </MudChip>
                                            </MudTd>
                                            @foreach (var dept in productionDepartments)
                                            {
                                                <MudTd DataLabel="@dept.Name" Style="text-align: center;">
                                                    @if (row.TargetDepartmentIds.Contains(dept.Id))
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" 
                                                            Color="Color.Success" Size="Size.Small" />
                                                    }
                                                    else
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.RadioButtonUnchecked" 
                                                            Color="Color.Default" Size="Size.Small" />
                                                    }
                                                </MudTd>
                                            }
                                            <MudTd DataLabel="可维修部门数" Style="text-align: center;">
                                                <MudChip T="string" Color="@(row.TargetDepartmentIds.Count > 0 ? Color.Info : Color.Default)" 
                                                    Size="Size.Small">
                                                    @row.TargetDepartmentIds.Count
                                                </MudChip>
                                            </MudTd>
                                        </RowTemplate>
                                    </MudTable>
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 权限详情列表 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">权限详情</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudTable Items="@allPermissions" Hover="true" Dense="true" Striped="true">
                                    <HeaderContent>
                                        <MudTh>维修部门</MudTh>
                                        <MudTh>目标部门</MudTh>
                                        <MudTh>状态</MudTh>
                                        <MudTh>创建时间</MudTh>
                                        <MudTh>备注</MudTh>
                                        <MudTh>操作</MudTh>
                                    </HeaderContent>
                                    <RowTemplate Context="permission">
                                        <MudTd DataLabel="维修部门">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Small" Class="mr-2" />
                                                <span>@permission.MaintenanceDepartment?.Name</span>
                                            </div>
                                        </MudTd>
                                        <MudTd DataLabel="目标部门">
                                            <div class="d-flex align-center">
                                                <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Small" Class="mr-2" />
                                                <span>@permission.TargetDepartment?.Name</span>
                                            </div>
                                        </MudTd>
                                        <MudTd DataLabel="状态">
                                            <MudChip T="string" Color="@(permission.IsEnabled ? Color.Success : Color.Default)" 
                                                Size="Size.Small">
                                                @(permission.IsEnabled ? "启用" : "禁用")
                                            </MudChip>
                                        </MudTd>
                                        <MudTd DataLabel="创建时间">
                                            <MudText Typo="Typo.caption">
                                                @permission.CreatedAt.ToString("yyyy-MM-dd HH:mm")
                                            </MudText>
                                        </MudTd>
                                        <MudTd DataLabel="备注">
                                            <MudText Typo="Typo.caption">
                                                @(string.IsNullOrEmpty(permission.Remark) ? "-" : permission.Remark)
                                            </MudText>
                                        </MudTd>
                                        <MudTd DataLabel="操作">
                                            <MudButtonGroup Size="Size.Small" Variant="Variant.Text">
                                                <MudButton Color="@(permission.IsEnabled ? Color.Warning : Color.Success)" 
                                                    StartIcon="@(permission.IsEnabled ? Icons.Material.Filled.ToggleOff : Icons.Material.Filled.ToggleOn)"
                                                    OnClick="@(() => TogglePermissionStatus(permission.Id, !permission.IsEnabled))">
                                                    @(permission.IsEnabled ? "禁用" : "启用")
                                                </MudButton>
                                                <MudButton Color="Color.Error" 
                                                    StartIcon="@Icons.Material.Filled.Delete"
                                                    OnClick="@(() => DeletePermission(permission.MaintenanceDepartmentId, permission.TargetDepartmentId))">
                                                    删除
                                                </MudButton>
                                            </MudButtonGroup>
                                        </MudTd>
                                    </RowTemplate>
                                </MudTable>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 统计信息 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">统计信息</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Engineering" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@maintenanceDepartments.Count</MudText>
                                                    <MudText Typo="Typo.body2">维修部门数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Factory" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@productionDepartments.Count</MudText>
                                                    <MudText Typo="Typo.body2">生产部门数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Link" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@GetTotalPermissions()</MudText>
                                                    <MudText Typo="Typo.body2">权限关系数</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                    <MudItem xs="6" sm="3">
                                        <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                            <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                                <MudIcon Icon="@Icons.Material.Filled.Percent" Size="Size.Large" />
                                                <div>
                                                    <MudText Typo="Typo.h4">@GetCoveragePercentage()%</MudText>
                                                    <MudText Typo="Typo.body2">权限覆盖率</MudText>
                                                </div>
                                            </MudStack>
                                        </MudPaper>
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudContainer>
    </Authorized>
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                </MudStack>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</AuthorizeView>

@code {
    private List<Department> maintenanceDepartments = new();
    private List<Department> productionDepartments = new();
    private List<MaintenanceDepartmentPermission> allPermissions = new();
    private List<PermissionMatrixRow> permissionMatrix = new();
    private Dictionary<int, HashSet<int>> maintenancePermissions = new(); // MaintenanceDepartmentId -> Set of TargetDepartmentIds
    private Department? selectedMaintenanceDepartment;
    private bool loading = false;
    private bool saving = false;

    protected override async Task OnInitializedAsync()
    {
        await LoadData();
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            // 获取维修类型的部门
            maintenanceDepartments = await DepartmentTypeService.GetMaintenanceDepartmentsAsync();

            // 加载部门类型信息
            foreach (var dept in maintenanceDepartments)
            {
                if (dept.DepartmentTypeId.HasValue)
                {
                    dept.DepartmentType = await DepartmentTypeService.GetDepartmentTypeByIdAsync(dept.DepartmentTypeId.Value);
                }
            }

            // 获取生产类型的部门
            productionDepartments = await DepartmentTypeService.GetProductionDepartmentsAsync();

            await LoadMaintenancePermissions();
            await LoadAllPermissions();
            BuildPermissionMatrix();
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            loading = false;
        }
    }

    private async Task LoadMaintenancePermissions()
    {
        maintenancePermissions.Clear();

        var permissionMatrix = await MaintenanceDepartmentPermissionService.GetMaintenanceDepartmentPermissionMatrixAsync();

        foreach (var permission in permissionMatrix)
        {
            maintenancePermissions[permission.Key] = new HashSet<int>(permission.Value);
        }
    }

    private async Task LoadAllPermissions()
    {
        allPermissions = await MaintenanceDepartmentPermissionService.GetAllMaintenanceDepartmentPermissionsAsync();
    }

    private void BuildPermissionMatrix()
    {
        permissionMatrix.Clear();

        foreach (var maintenanceDept in maintenanceDepartments)
        {
            var matrixRow = new PermissionMatrixRow
            {
                MaintenanceDepartmentId = maintenanceDept.Id,
                MaintenanceDepartmentName = maintenanceDept.Name,
                TargetDepartmentIds = maintenancePermissions.GetValueOrDefault(maintenanceDept.Id, new HashSet<int>()).ToList()
            };
            permissionMatrix.Add(matrixRow);
        }
    }

    private bool IsMaintenancePermissionAssigned(int maintenanceDepartmentId, int targetDepartmentId)
    {
        return maintenancePermissions.GetValueOrDefault(maintenanceDepartmentId, new HashSet<int>()).Contains(targetDepartmentId);
    }

    private void OnMaintenancePermissionChanged(int maintenanceDepartmentId, int targetDepartmentId, bool isAssigned)
    {
        if (!maintenancePermissions.ContainsKey(maintenanceDepartmentId))
        {
            maintenancePermissions[maintenanceDepartmentId] = new HashSet<int>();
        }

        if (isAssigned)
        {
            maintenancePermissions[maintenanceDepartmentId].Add(targetDepartmentId);
        }
        else
        {
            maintenancePermissions[maintenanceDepartmentId].Remove(targetDepartmentId);
        }
    }

    private async Task SaveMaintenancePermissions()
    {
        if (selectedMaintenanceDepartment == null) return;

        saving = true;
        try
        {
            var targetDepartmentIds = maintenancePermissions.GetValueOrDefault(selectedMaintenanceDepartment.Id, new HashSet<int>()).ToList();

            var result = await MaintenanceDepartmentPermissionService.SetMaintenanceDepartmentPermissionsAsync(selectedMaintenanceDepartment.Id, targetDepartmentIds);

            if (result)
            {
                Snackbar.Add($"维修部门 {selectedMaintenanceDepartment.Name} 的权限已保存", Severity.Success);
                await LoadMaintenancePermissions();
                await LoadAllPermissions();
                BuildPermissionMatrix();
            }
            else
            {
                Snackbar.Add("保存失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"保存失败: {ex.Message}", Severity.Error);
        }
        finally
        {
            saving = false;
        }
    }

    private async Task TogglePermissionStatus(int permissionId, bool isEnabled)
    {
        try
        {
            var result = await MaintenanceDepartmentPermissionService.ToggleMaintenanceDepartmentPermissionAsync(permissionId, isEnabled);

            if (result)
            {
                Snackbar.Add($"权限状态已{(isEnabled ? "启用" : "禁用")}", Severity.Success);
                await LoadAllPermissions();
                await LoadMaintenancePermissions();
                BuildPermissionMatrix();
            }
            else
            {
                Snackbar.Add("操作失败", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"操作失败: {ex.Message}", Severity.Error);
        }
    }

    private async Task DeletePermission(int maintenanceDepartmentId, int targetDepartmentId)
    {
        var confirmed = await DialogService.ShowMessageBox(
            "确认删除",
            "确定要删除这个维修权限关系吗？",
            yesText: "删除", cancelText: "取消");

        if (confirmed == true)
        {
            try
            {
                var result = await MaintenanceDepartmentPermissionService.DeleteMaintenanceDepartmentPermissionAsync(maintenanceDepartmentId, targetDepartmentId);

                if (result)
                {
                    Snackbar.Add("权限关系已删除", Severity.Success);
                    await LoadAllPermissions();
                    await LoadMaintenancePermissions();
                    BuildPermissionMatrix();
                }
                else
                {
                    Snackbar.Add("删除失败", Severity.Error);
                }
            }
            catch (Exception ex)
            {
                Snackbar.Add($"删除失败: {ex.Message}", Severity.Error);
            }
        }
    }

    private int GetTotalPermissions()
    {
        return allPermissions.Count(p => p.IsEnabled);
    }

    private int GetCoveragePercentage()
    {
        if (maintenanceDepartments.Count == 0 || productionDepartments.Count == 0) return 0;
        var totalPossible = maintenanceDepartments.Count * productionDepartments.Count;
        var totalAssigned = GetTotalPermissions();
        return (int)Math.Round((double)totalAssigned / totalPossible * 100);
    }

    public class PermissionMatrixRow
    {
        public int MaintenanceDepartmentId { get; set; }
        public string MaintenanceDepartmentName { get; set; } = string.Empty;
        public List<int> TargetDepartmentIds { get; set; } = new();
    }
}
