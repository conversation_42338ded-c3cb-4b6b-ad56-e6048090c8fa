using CoreHub.Shared.Data;
using CoreHub.Shared.Models.Database;
using Microsoft.Extensions.Logging;
using SqlSugar;

namespace CoreHub.Shared.Services
{
    /// <summary>
    /// 维修部门权限服务接口
    /// </summary>
    public interface IMaintenanceDepartmentPermissionService
    {
        #region 权限查询

        /// <summary>
        /// 获取维修部门可以维修的目标部门列表
        /// </summary>
        Task<List<Department>> GetMaintenanceTargetDepartmentsAsync(int maintenanceDepartmentId);

        /// <summary>
        /// 获取可以维修指定部门设备的维修部门列表
        /// </summary>
        Task<List<Department>> GetAvailableMaintenanceDepartmentsAsync(int targetDepartmentId);

        /// <summary>
        /// 检查维修部门是否可以维修指定部门的设备
        /// </summary>
        Task<bool> CanMaintenanceDepartmentRepairAsync(int maintenanceDepartmentId, int targetDepartmentId);

        /// <summary>
        /// 获取所有维修部门权限关系
        /// </summary>
        Task<List<MaintenanceDepartmentPermission>> GetAllMaintenanceDepartmentPermissionsAsync();

        /// <summary>
        /// 获取维修部门权限矩阵
        /// </summary>
        Task<Dictionary<int, List<int>>> GetMaintenanceDepartmentPermissionMatrixAsync();

        #endregion

        #region 权限管理

        /// <summary>
        /// 创建维修部门权限
        /// </summary>
        Task<bool> CreateMaintenanceDepartmentPermissionAsync(int maintenanceDepartmentId, int targetDepartmentId, string? remark = null);

        /// <summary>
        /// 删除维修部门权限
        /// </summary>
        Task<bool> DeleteMaintenanceDepartmentPermissionAsync(int maintenanceDepartmentId, int targetDepartmentId);

        /// <summary>
        /// 批量设置维修部门的目标部门权限
        /// </summary>
        Task<bool> SetMaintenanceDepartmentPermissionsAsync(int maintenanceDepartmentId, List<int> targetDepartmentIds);

        /// <summary>
        /// 启用/禁用维修部门权限
        /// </summary>
        Task<bool> ToggleMaintenanceDepartmentPermissionAsync(int permissionId, bool isEnabled);

        #endregion
    }

    /// <summary>
    /// 维修部门权限服务实现
    /// </summary>
    public class MaintenanceDepartmentPermissionService : IMaintenanceDepartmentPermissionService
    {
        private readonly DatabaseContext _dbContext;
        private readonly ILogger<MaintenanceDepartmentPermissionService> _logger;

        public MaintenanceDepartmentPermissionService(DatabaseContext dbContext, ILogger<MaintenanceDepartmentPermissionService> logger)
        {
            _dbContext = dbContext;
            _logger = logger;
        }

        #region 权限查询

        public async Task<List<Department>> GetMaintenanceTargetDepartmentsAsync(int maintenanceDepartmentId)
        {
            try
            {
                var targetDepartmentIds = await _dbContext.Db.Queryable<MaintenanceDepartmentPermission>()
                    .Where(mdp => mdp.MaintenanceDepartmentId == maintenanceDepartmentId && mdp.IsEnabled)
                    .Select(mdp => mdp.TargetDepartmentId)
                    .ToListAsync();

                if (!targetDepartmentIds.Any())
                {
                    return new List<Department>();
                }

                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => targetDepartmentIds.Contains(d.Id) && d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修部门目标部门列表失败：{maintenanceDepartmentId}", maintenanceDepartmentId);
                return new List<Department>();
            }
        }

        public async Task<List<Department>> GetAvailableMaintenanceDepartmentsAsync(int targetDepartmentId)
        {
            try
            {
                var maintenanceDepartmentIds = await _dbContext.Db.Queryable<MaintenanceDepartmentPermission>()
                    .Where(mdp => mdp.TargetDepartmentId == targetDepartmentId && mdp.IsEnabled)
                    .Select(mdp => mdp.MaintenanceDepartmentId)
                    .ToListAsync();

                if (!maintenanceDepartmentIds.Any())
                {
                    return new List<Department>();
                }

                return await _dbContext.Db.Queryable<Department>()
                    .Where(d => maintenanceDepartmentIds.Contains(d.Id) && d.IsEnabled)
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取可用维修部门列表失败：{targetDepartmentId}", targetDepartmentId);
                return new List<Department>();
            }
        }

        public async Task<bool> CanMaintenanceDepartmentRepairAsync(int maintenanceDepartmentId, int targetDepartmentId)
        {
            try
            {
                return await _dbContext.Db.Queryable<MaintenanceDepartmentPermission>()
                    .Where(mdp => mdp.MaintenanceDepartmentId == maintenanceDepartmentId && 
                                 mdp.TargetDepartmentId == targetDepartmentId && 
                                 mdp.IsEnabled)
                    .AnyAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "检查维修部门权限失败：{maintenanceDepartmentId}, {targetDepartmentId}", maintenanceDepartmentId, targetDepartmentId);
                return false;
            }
        }

        public async Task<List<MaintenanceDepartmentPermission>> GetAllMaintenanceDepartmentPermissionsAsync()
        {
            try
            {
                return await _dbContext.Db.Queryable<MaintenanceDepartmentPermission>()
                    .LeftJoin<Department>((mdp, md) => mdp.MaintenanceDepartmentId == md.Id)
                    .LeftJoin<Department>((mdp, md, td) => mdp.TargetDepartmentId == td.Id)
                    .Select((mdp, md, td) => new MaintenanceDepartmentPermission
                    {
                        Id = mdp.Id,
                        MaintenanceDepartmentId = mdp.MaintenanceDepartmentId,
                        TargetDepartmentId = mdp.TargetDepartmentId,
                        IsEnabled = mdp.IsEnabled,
                        CreatedAt = mdp.CreatedAt,
                        Remark = mdp.Remark,
                        MaintenanceDepartment = md,
                        TargetDepartment = td
                    })
                    .ToListAsync();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取所有维修部门权限失败");
                return new List<MaintenanceDepartmentPermission>();
            }
        }

        public async Task<Dictionary<int, List<int>>> GetMaintenanceDepartmentPermissionMatrixAsync()
        {
            try
            {
                var permissions = await _dbContext.Db.Queryable<MaintenanceDepartmentPermission>()
                    .Where(mdp => mdp.IsEnabled)
                    .Select(mdp => new { mdp.MaintenanceDepartmentId, mdp.TargetDepartmentId })
                    .ToListAsync();

                return permissions
                    .GroupBy(p => p.MaintenanceDepartmentId)
                    .ToDictionary(
                        g => g.Key,
                        g => g.Select(p => p.TargetDepartmentId).ToList()
                    );
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "获取维修部门权限矩阵失败");
                return new Dictionary<int, List<int>>();
            }
        }

        #endregion

        #region 权限管理

        public async Task<bool> CreateMaintenanceDepartmentPermissionAsync(int maintenanceDepartmentId, int targetDepartmentId, string? remark = null)
        {
            try
            {
                // 检查是否已存在
                var exists = await CanMaintenanceDepartmentRepairAsync(maintenanceDepartmentId, targetDepartmentId);
                if (exists)
                {
                    _logger.LogWarning("维修部门权限已存在：{maintenanceDepartmentId} -> {targetDepartmentId}", maintenanceDepartmentId, targetDepartmentId);
                    return true;
                }

                var permission = new MaintenanceDepartmentPermission
                {
                    MaintenanceDepartmentId = maintenanceDepartmentId,
                    TargetDepartmentId = targetDepartmentId,
                    IsEnabled = true,
                    CreatedAt = DateTime.Now,
                    Remark = remark
                };

                var result = await _dbContext.Db.Insertable(permission).ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "创建维修部门权限失败：{maintenanceDepartmentId} -> {targetDepartmentId}", maintenanceDepartmentId, targetDepartmentId);
                return false;
            }
        }

        public async Task<bool> DeleteMaintenanceDepartmentPermissionAsync(int maintenanceDepartmentId, int targetDepartmentId)
        {
            try
            {
                var result = await _dbContext.Db.Deleteable<MaintenanceDepartmentPermission>()
                    .Where(mdp => mdp.MaintenanceDepartmentId == maintenanceDepartmentId && 
                                 mdp.TargetDepartmentId == targetDepartmentId)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "删除维修部门权限失败：{maintenanceDepartmentId} -> {targetDepartmentId}", maintenanceDepartmentId, targetDepartmentId);
                return false;
            }
        }

        public async Task<bool> SetMaintenanceDepartmentPermissionsAsync(int maintenanceDepartmentId, List<int> targetDepartmentIds)
        {
            try
            {
                _logger.LogInformation("开始设置维修部门权限：maintenanceDepartmentId={maintenanceDepartmentId}, targetDepartmentIds={targetDepartmentIds}",
                    maintenanceDepartmentId, string.Join(",", targetDepartmentIds));

                // 检查表是否存在
                try
                {
                    var tableExists = await _dbContext.Db.Ado.SqlQueryAsync<int>("SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = 'MaintenanceDepartmentPermissions'");
                    _logger.LogInformation("MaintenanceDepartmentPermissions表存在检查结果：{exists}", tableExists.FirstOrDefault() > 0);
                }
                catch (Exception checkEx)
                {
                    _logger.LogError(checkEx, "检查表存在性失败");
                }

                // 开始事务
                await _dbContext.Db.Ado.BeginTranAsync();
                _logger.LogInformation("事务已开始");

                try
                {
                    // 删除现有权限
                    var deleteResult = await _dbContext.Db.Deleteable<MaintenanceDepartmentPermission>()
                        .Where(mdp => mdp.MaintenanceDepartmentId == maintenanceDepartmentId)
                        .ExecuteCommandAsync();
                    _logger.LogInformation("删除现有维修权限：删除了{count}条记录", deleteResult);

                    // 插入新权限
                    if (targetDepartmentIds.Any())
                    {
                        var permissions = targetDepartmentIds.Select(targetId => new MaintenanceDepartmentPermission
                        {
                            MaintenanceDepartmentId = maintenanceDepartmentId,
                            TargetDepartmentId = targetId,
                            IsEnabled = true,
                            CreatedAt = DateTime.Now
                        }).ToList();

                        var insertResult = await _dbContext.Db.Insertable(permissions).ExecuteCommandAsync();
                        _logger.LogInformation("插入新维修权限：插入了{count}条记录", insertResult);
                    }
                    else
                    {
                        _logger.LogInformation("没有需要插入的维修权限");
                    }

                    await _dbContext.Db.Ado.CommitTranAsync();
                    _logger.LogInformation("事务已提交，维修部门权限设置成功");
                    return true;
                }
                catch (Exception innerEx)
                {
                    await _dbContext.Db.Ado.RollbackTranAsync();
                    _logger.LogError(innerEx, "事务执行失败，已回滚");
                    throw;
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "批量设置维修部门权限失败：{maintenanceDepartmentId}", maintenanceDepartmentId);
                return false;
            }
        }

        public async Task<bool> ToggleMaintenanceDepartmentPermissionAsync(int permissionId, bool isEnabled)
        {
            try
            {
                var result = await _dbContext.Db.Updateable<MaintenanceDepartmentPermission>()
                    .SetColumns(mdp => new MaintenanceDepartmentPermission 
                    { 
                        IsEnabled = isEnabled, 
                        UpdatedAt = DateTime.Now 
                    })
                    .Where(mdp => mdp.Id == permissionId)
                    .ExecuteCommandAsync();
                return result > 0;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "切换维修部门权限状态失败：{permissionId}", permissionId);
                return false;
            }
        }

        #endregion
    }
}
