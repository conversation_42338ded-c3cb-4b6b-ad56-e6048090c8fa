@page "/role-department-assignment-management"
@using CoreHub.Shared.Models.Database
@using CoreHub.Shared.Services
@using Microsoft.AspNetCore.Components.Authorization
@using System.Security.Claims
@using MudBlazor
@inject IRoleDepartmentAssignmentServiceV2 AssignmentService
@inject ISnackbar Snackbar
@inject AuthenticationStateProvider AuthenticationStateProvider

<PageTitle>角色部门分配管理</PageTitle>

<AuthorizeView>
    <Authorized>
        <MudContainer MaxWidth="MaxWidth.ExtraLarge" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudGrid>
                    <!-- 页面标题 -->
                    <MudItem xs="12">
                        <MudStack Row AlignItems="AlignItems.Center" Spacing="3" Class="mb-6">
                            <MudIcon Icon="@Icons.Material.Filled.Assignment" Size="Size.Large" Color="Color.Primary" />
                            <div>
                                <MudText Typo="Typo.h4">角色部门分配管理</MudText>
                                <MudText Typo="Typo.body1" Color="Color.Secondary">
                                    管理角色与部门的分配关系，控制用户的部门访问权限
                                </MudText>
                            </div>
                            <MudSpacer />
                            <MudButton Variant="Variant.Outlined" Color="Color.Primary" 
                                StartIcon="@Icons.Material.Filled.Refresh" OnClick="LoadData">
                                刷新数据
                            </MudButton>
                        </MudStack>
                    </MudItem>

                    <!-- 统计信息 -->
                    <MudItem xs="12">
                        <MudGrid>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Group" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@statistics.TotalRoles</MudText>
                                            <MudText Typo="Typo.body2">总角色数</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Business" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@statistics.TotalDepartments</MudText>
                                            <MudText Typo="Typo.body2">总部门数</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Link" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@statistics.TotalAssignments</MudText>
                                            <MudText Typo="Typo.body2">总分配数</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                            <MudItem xs="6" sm="3">
                                <MudPaper Class="pa-4" Style="background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%); color: white;">
                                    <MudStack Row AlignItems="AlignItems.Center" Spacing="2">
                                        <MudIcon Icon="@Icons.Material.Filled.Percent" Size="Size.Large" />
                                        <div>
                                            <MudText Typo="Typo.h4">@statistics.AssignmentRate.ToString("F1")%</MudText>
                                            <MudText Typo="Typo.body2">角色覆盖率</MudText>
                                        </div>
                                    </MudStack>
                                </MudPaper>
                            </MudItem>
                        </MudGrid>
                    </MudItem>

                    <!-- 角色选择和分配操作 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">角色部门分配</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                <MudGrid>
                                    <!-- 角色选择 -->
                                    <MudItem xs="12" md="4">
                                        <MudSelect T="RoleDepartmentAssignmentViewModel" Value="selectedRoleAssignment"
                                            For="@(() => selectedRoleAssignment)" Label="选择角色" Variant="Variant.Outlined"
                                            ToStringFunc="@(r => r?.RoleName ?? "")" ValueChanged="OnRoleSelectionChanged">
                                            @foreach (var roleAssignment in roleAssignments)
                                            {
                                                <MudSelectItem T="RoleDepartmentAssignmentViewModel" Value="@roleAssignment">
                                                    <div class="d-flex align-center justify-space-between">
                                                        <span>@roleAssignment.RoleName</span>
                                                        <MudChip T="string" Color="Color.Info" Size="Size.Small">
                                                            @roleAssignment.AssignedCount/@roleAssignment.Departments.Count
                                                        </MudChip>
                                                    </div>
                                                </MudSelectItem>
                                            }
                                        </MudSelect>
                                    </MudItem>

                                    <!-- 快速操作 -->
                                    <MudItem xs="12" md="8">
                                        @if (selectedRoleAssignment != null)
                                        {
                                            <MudStack Row Spacing="2" AlignItems="AlignItems.Center">
                                                <MudButton Variant="Variant.Filled" Color="Color.Success" Size="Size.Small"
                                                    OnClick="SelectAllDepartments" StartIcon="@Icons.Material.Filled.SelectAll">
                                                    全选
                                                </MudButton>
                                                <MudButton Variant="Variant.Filled" Color="Color.Warning" Size="Size.Small"
                                                    OnClick="ClearAllDepartments" StartIcon="@Icons.Material.Filled.Clear">
                                                    清空
                                                </MudButton>
                                                <MudButton Variant="Variant.Filled" Color="Color.Primary" Size="Size.Small"
                                                    OnClick="SaveAssignments" StartIcon="@Icons.Material.Filled.Save"
                                                    Disabled="@saving">
                                                    @if (saving)
                                                    {
                                                        <MudProgressCircular Class="ms-n1" Size="Size.Small" Indeterminate="true"/>
                                                        <MudText Class="ms-2">保存中...</MudText>
                                                    }
                                                    else
                                                    {
                                                        <MudText>保存分配</MudText>
                                                    }
                                                </MudButton>
                                                <MudSpacer />
                                                <MudText Typo="Typo.body2" Color="Color.Secondary">
                                                    已分配 @selectedRoleAssignment.AssignedCount 个部门
                                                </MudText>
                                            </MudStack>
                                        }
                                    </MudItem>

                                    <!-- 部门分配列表 -->
                                    <MudItem xs="12">
                                        @if (selectedRoleAssignment != null)
                                        {
                                            <MudText Typo="Typo.subtitle1" Class="mb-3">
                                                为角色 "@selectedRoleAssignment.RoleName" 分配部门
                                            </MudText>
                                            <MudGrid>
                                                @foreach (var dept in selectedRoleAssignment.Departments)
                                                {
                                                    <MudItem xs="6" sm="4" md="3">
                                                        <MudCheckBox T="bool"
                                                            Value="@dept.IsAssigned"
                                                            ValueChanged="@((bool isChecked) => OnDepartmentToggle(dept, isChecked))"
                                                            Label="@dept.DepartmentName"
                                                            Color="Color.Primary" />
                                                    </MudItem>
                                                }
                                            </MudGrid>
                                        }
                                        else
                                        {
                                            <MudAlert Severity="Severity.Info">
                                                请选择一个角色来管理其部门分配
                                            </MudAlert>
                                        }
                                    </MudItem>
                                </MudGrid>
                            </MudCardContent>
                        </MudCard>
                    </MudItem>

                    <!-- 分配矩阵视图 -->
                    <MudItem xs="12">
                        <MudCard>
                            <MudCardHeader>
                                <CardHeaderContent>
                                    <MudText Typo="Typo.h6">分配矩阵视图</MudText>
                                </CardHeaderContent>
                            </MudCardHeader>
                            <MudCardContent>
                                @if (loading)
                                {
                                    <MudProgressLinear Color="Color.Primary" Indeterminate="true" Class="my-7" />
                                }
                                else if (roleAssignments.Any())
                                {
                                    <MudTable Items="@roleAssignments" Hover="true" Dense="true" Striped="true" FixedHeader="true" Height="400px">
                                        <HeaderContent>
                                            <MudTh Style="min-width: 120px;">角色</MudTh>
                                            @foreach (var dept in GetAllDepartments())
                                            {
                                                <MudTh Style="text-align: center; min-width: 80px;">
                                                    <MudText Typo="Typo.caption">@dept.DepartmentName</MudText>
                                                </MudTh>
                                            }
                                            <MudTh Style="text-align: center; min-width: 100px;">分配统计</MudTh>
                                        </HeaderContent>
                                        <RowTemplate Context="roleAssignment">
                                            <MudTd DataLabel="角色">
                                                <MudChip T="string" Color="Color.Primary" Size="Size.Small">
                                                    @roleAssignment.RoleName
                                                </MudChip>
                                            </MudTd>
                                            @foreach (var dept in GetAllDepartments())
                                            {
                                                <MudTd DataLabel="@dept.DepartmentName" Style="text-align: center;">
                                                    @{
                                                        var isAssigned = roleAssignment.Departments.Any(d => d.DepartmentId == dept.DepartmentId && d.IsAssigned);
                                                    }
                                                    @if (isAssigned)
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.CheckCircle" 
                                                            Color="Color.Success" Size="Size.Small" />
                                                    }
                                                    else
                                                    {
                                                        <MudIcon Icon="@Icons.Material.Filled.RadioButtonUnchecked" 
                                                            Color="Color.Default" Size="Size.Small" />
                                                    }
                                                </MudTd>
                                            }
                                            <MudTd DataLabel="分配统计" Style="text-align: center;">
                                                <MudChip T="string" Color="@(roleAssignment.AssignedCount > 0 ? Color.Info : Color.Default)" 
                                                    Size="Size.Small">
                                                    @roleAssignment.AssignedCount/@roleAssignment.Departments.Count
                                                </MudChip>
                                            </MudTd>
                                        </RowTemplate>
                                    </MudTable>
                                }
                                else
                                {
                                    <MudAlert Severity="Severity.Warning">
                                        暂无角色分配数据
                                    </MudAlert>
                                }
                            </MudCardContent>
                        </MudCard>
                    </MudItem>
                </MudGrid>
            </MudPaper>
        </MudContainer>
    </Authorized>
    <NotAuthorized>
        <MudContainer MaxWidth="MaxWidth.Large" Fixed="true" Class="mt-4">
            <MudPaper Class="pa-6">
                <MudStack AlignItems="AlignItems.Center" Spacing="4">
                    <MudIcon Icon="@Icons.Material.Filled.Lock" Size="Size.Large" Color="Color.Warning" />
                    <MudText Typo="Typo.h5">需要登录</MudText>
                    <MudText Typo="Typo.body1" Color="Color.Secondary">请先登录后再访问此页面</MudText>
                </MudStack>
            </MudPaper>
        </MudContainer>
    </NotAuthorized>
</AuthorizeView>

@code {
    private List<RoleDepartmentAssignmentViewModel> roleAssignments = new();
    private RoleDepartmentAssignmentViewModel? selectedRoleAssignment;
    private AssignmentStatistics statistics = new();
    private bool loading = false;
    private bool saving = false;
    private int? currentUserId;

    protected override async Task OnInitializedAsync()
    {
        await GetCurrentUser();
        await LoadData();
    }

    private async Task GetCurrentUser()
    {
        try
        {
            var authState = await AuthenticationStateProvider.GetAuthenticationStateAsync();
            if (authState.User.Identity?.IsAuthenticated == true)
            {
                var userIdClaim = authState.User.FindFirst(ClaimTypes.NameIdentifier);
                if (userIdClaim != null && int.TryParse(userIdClaim.Value, out var userId))
                {
                    currentUserId = userId;
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"获取当前用户失败: {ex.Message}");
        }
    }

    private async Task LoadData()
    {
        loading = true;
        try
        {
            // 并行加载数据
            var assignmentsTask = AssignmentService.GetAllRoleAssignmentsAsync();
            var statisticsTask = AssignmentService.GetAssignmentStatisticsAsync();

            await Task.WhenAll(assignmentsTask, statisticsTask);

            roleAssignments = await assignmentsTask;
            statistics = await statisticsTask;

            // 如果之前有选中的角色，重新选中
            if (selectedRoleAssignment != null)
            {
                selectedRoleAssignment = roleAssignments.FirstOrDefault(r => r.RoleId == selectedRoleAssignment.RoleId);
            }

            Snackbar.Add($"数据加载完成：{roleAssignments.Count} 个角色，{statistics.TotalAssignments} 个分配", Severity.Success);
        }
        catch (Exception ex)
        {
            Snackbar.Add($"加载数据失败: {ex.Message}", Severity.Error);
            Console.WriteLine($"加载数据异常: {ex}");
        }
        finally
        {
            loading = false;
        }
    }

    private void OnRoleSelectionChanged(RoleDepartmentAssignmentViewModel? role)
    {
        Console.WriteLine($"[DEBUG] 角色选择变更: {role?.RoleName ?? "null"}");
        selectedRoleAssignment = role;

        if (role != null)
        {
            Console.WriteLine($"[DEBUG] 选中角色 {role.RoleName}, 部门数量: {role.Departments.Count}");
            foreach (var dept in role.Departments)
            {
                Console.WriteLine($"[DEBUG] 部门: {dept.DepartmentName}, 是否分配: {dept.IsAssigned}");
            }
        }

        StateHasChanged();
    }

    private void OnDepartmentToggle(DepartmentAssignmentItem dept, bool isAssigned)
    {
        Console.WriteLine($"[DEBUG] 部门切换: {dept.DepartmentName}, 新状态: {isAssigned}");
        dept.IsAssigned = isAssigned;
        StateHasChanged();
    }

    private void SelectAllDepartments()
    {
        if (selectedRoleAssignment == null) return;

        foreach (var dept in selectedRoleAssignment.Departments)
        {
            dept.IsAssigned = true;
        }
        StateHasChanged();
    }

    private void ClearAllDepartments()
    {
        if (selectedRoleAssignment == null) return;

        foreach (var dept in selectedRoleAssignment.Departments)
        {
            dept.IsAssigned = false;
        }
        StateHasChanged();
    }

    private async Task SaveAssignments()
    {
        if (selectedRoleAssignment == null)
        {
            Snackbar.Add("请先选择角色", Severity.Warning);
            return;
        }

        saving = true;
        try
        {
            var assignedDepartmentIds = selectedRoleAssignment.Departments
                .Where(d => d.IsAssigned)
                .Select(d => d.DepartmentId)
                .ToList();

            Console.WriteLine($"[SAVE] 角色 {selectedRoleAssignment.RoleId}({selectedRoleAssignment.RoleName}) 分配部门: [{string.Join(",", assignedDepartmentIds)}]");

            var result = await AssignmentService.SetRoleAssignmentsAsync(
                selectedRoleAssignment.RoleId,
                assignedDepartmentIds,
                currentUserId);

            if (result)
            {
                Snackbar.Add($"✅ 角色 {selectedRoleAssignment.RoleName} 的部门分配已保存", Severity.Success);

                // 重新加载数据以确保同步
                await LoadData();
            }
            else
            {
                Snackbar.Add("❌ 保存失败，请检查网络连接和权限", Severity.Error);
            }
        }
        catch (Exception ex)
        {
            Snackbar.Add($"❌ 保存失败: {ex.Message}", Severity.Error);
            Console.WriteLine($"[ERROR] 保存分配失败: {ex}");
        }
        finally
        {
            saving = false;
        }
    }

    private List<DepartmentAssignmentItem> GetAllDepartments()
    {
        if (!roleAssignments.Any()) return new List<DepartmentAssignmentItem>();

        // 从第一个角色获取所有部门列表（所有角色的部门列表应该是相同的）
        return roleAssignments.First().Departments
            .Select(d => new DepartmentAssignmentItem
            {
                DepartmentId = d.DepartmentId,
                DepartmentName = d.DepartmentName,
                DepartmentCode = d.DepartmentCode
            })
            .ToList();
    }
}
