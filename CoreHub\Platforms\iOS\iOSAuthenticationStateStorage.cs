using Foundation;
using CoreHub.Shared.Services;

namespace CoreHub.Platforms.iOS
{
    /// <summary>
    /// iOS平台认证状态存储实现 - 使用NSUserDefaults
    /// </summary>
    public class iOSAuthenticationStateStorage : IAuthenticationStateStorage
    {
        private readonly NSUserDefaults _userDefaults;

        public iOSAuthenticationStateStorage()
        {
            _userDefaults = NSUserDefaults.StandardUserDefaults;
        }

        public bool IsAvailable => _userDefaults != null;

        public Task SaveAuthStateAsync(string key, string data)
        {
            try
            {
                _userDefaults.SetString(data, key);
                _userDefaults.Synchronize();
                
                System.Diagnostics.Debug.WriteLine($"iOS存储保存成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"iOS存储保存失败: {ex.Message}");
                throw;
            }
        }

        public Task<string?> GetAuthStateAsync(string key)
        {
            try
            {
                var data = _userDefaults.StringForKey(key);
                System.Diagnostics.Debug.WriteLine($"iOS存储读取: {key} = {(data != null ? "有数据" : "无数据")}");
                return Task.FromResult(data);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"iOS存储读取失败: {ex.Message}");
                return Task.FromResult<string?>(null);
            }
        }

        public Task ClearAuthStateAsync(string key)
        {
            try
            {
                _userDefaults.RemoveObject(key);
                _userDefaults.Synchronize();
                
                System.Diagnostics.Debug.WriteLine($"iOS存储清除成功: {key}");
                return Task.CompletedTask;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"iOS存储清除失败: {ex.Message}");
                return Task.CompletedTask;
            }
        }
    }
} 