# Android 通知功能测试指南

## 功能概述

本项目已经实现了完整的Android手机通知功能，包括：

1. **通知权限管理** - 自动请求Android 13+的通知权限
2. **通知发送** - 支持发送带标题和内容的通知
3. **通知渠道** - 创建专用的通知渠道
4. **振动反馈** - 通知时提供振动反馈
5. **点击响应** - 点击通知可以打开应用

## 已实现的功能

### 1. Android清单权限
在 `Platforms/Android/AndroidManifest.xml` 中添加了必要的权限：
- `POST_NOTIFICATIONS` - Android 13+通知权限
- `VIBRATE` - 振动权限
- `WAKE_LOCK` - 唤醒设备权限

### 2. 通知服务实现
在 `Platforms/Android/NotificationService.cs` 中实现了：
- 通知渠道创建
- 权限检查和请求
- 通知发送功能
- 振动模式设置

### 3. Blazor页面集成
在 `MauiApp5.Shared/Pages/Counter.razor` 中添加了三个测试按钮：
- **发送通知** - 发送基本测试通知
- **发送计数通知** - 发送包含当前计数值的通知
- **请求通知权限** - 手动请求通知权限

## 测试步骤

### 1. 构建和部署
```bash
# 构建Android版本
dotnet build -f net8.0-android

# 部署到Android设备或模拟器
dotnet run -f net8.0-android
```

### 2. 测试通知功能

1. **启动应用**
   - 在Android设备或模拟器上启动MauiApp5应用

2. **导航到Counter页面**
   - 点击底部导航栏的"Counter"选项

3. **测试权限请求**
   - 点击"请求通知权限"按钮
   - 在Android 13+设备上会弹出权限请求对话框
   - 选择"允许"以授予通知权限

4. **测试基本通知**
   - 点击"发送通知"按钮
   - 应该在通知栏看到标题为"测试通知"的通知
   - 通知内容为"这是一个来自MauiApp5的测试通知！"
   - 设备应该有振动反馈

5. **测试计数通知**
   - 先点击"Click me"按钮几次增加计数
   - 然后点击"发送计数通知"按钮
   - 应该看到包含当前计数值的通知

6. **测试通知点击**
   - 点击通知栏中的通知
   - 应用应该被打开或切换到前台

## 预期效果

### 通知外观
- **小图标**: 系统默认信息图标
- **标题**: 自定义标题文本
- **内容**: 自定义消息内容
- **振动**: 短振动模式 (0ms, 250ms, 250ms, 250ms)
- **自动取消**: 点击后自动消失

### 权限处理
- **Android 13+**: 需要用户手动授权POST_NOTIFICATIONS权限
- **Android 12及以下**: 自动拥有通知权限
- **权限被拒绝**: 通知发送会静默失败，在调试输出中显示错误

## 调试信息

所有通知相关的操作都会在调试输出中记录：
- 权限请求结果
- 通知发送成功/失败
- 错误信息

可以在Visual Studio的输出窗口或Android Studio的Logcat中查看这些信息。

## 注意事项

1. **设备要求**: 需要Android 7.0 (API 24) 或更高版本
2. **权限**: Android 13+需要用户手动授权通知权限
3. **测试环境**: 建议在真实设备上测试，模拟器可能不完全支持通知功能
4. **系统设置**: 确保设备的通知设置允许应用发送通知

## 故障排除

如果通知不显示，请检查：
1. 应用是否有通知权限
2. 设备的"勿扰模式"是否开启
3. 应用的通知设置是否被禁用
4. 系统通知设置是否正常

## 扩展功能

当前实现支持进一步扩展：
- 添加通知图标和图片
- 支持通知操作按钮
- 实现通知分组
- 添加进度条通知
- 支持富文本通知内容 